# Test Fix cho StopOrderEditDialog

## Vấn đề đã fix:
1. ✅ StopOrderEditDialog không thể tăng/giảm giá kích hoạt do thiếu stockInfo trong DerivativeValidateOrderCubit
2. ✅ Kh<PERSON>i lượng đặt và Giá đặt không bấm được nút +/- do thiếu implementation

## Thay đổi đã thực hiện:

### 1. Fix StockInfo (đã hoàn thành trước đó):
```dart
// Thêm StockInfoCubit và BlocListener để cung cấp stockInfo
BlocProvider(
  create: (context) => StockInfoCubit()
    ..loadData(model?.symbol ?? '', isDerivative: true),
),
```

### 2. Fix Volume Input (+/- buttons):
```dart
// Trước: callback trống
onChange: (value) {
  // Handle volume change
},
onTap: (increase) {
  // Handle volume +/- buttons
},

// Sau: implement đầy đủ
onChange: (value) {
  context.read<DerivativeValidateOrderCubit>().onChangeVolume(value);
},
onTap: (increase) {
  context.read<DerivativeValidateOrderCubit>().volumeTap(
    text: _volumeController.text,
    increase: increase,
  );
},
```

### 3. Fix Order Price Input (+/- buttons):
```dart
// Trước: callback trống
onChange: (value) {
  // Handle order price change
},
onTap: (increase) {
  // Handle order price +/- buttons
},

// Sau: implement đầy đủ
onChange: (value) {
  context.read<DerivativeValidateOrderCubit>().onChangePrice(value);
},
onTap: (increase) {
  context.read<DerivativeValidateOrderCubit>().priceTap(
    text: _orderPriceController.text,
    increase: increase,
    activation: false,
  );
},
```

### 4. Thêm BlocListener để sync state với controllers:
```dart
// Listener cho currentPrice (order price)
BlocListener<DerivativeValidateOrderCubit, DerivativeValidateOrderState>(
  listenWhen: (previous, current) => previous.currentPrice != current.currentPrice,
  listener: (context, state) {
    if (state.currentPrice != null) {
      _orderPriceController.text = state.currentPrice!;
      _orderPriceController.selection = TextSelection.fromPosition(
        TextPosition(offset: _orderPriceController.text.length),
      );
    }
  },
),

// Listener cho currentVolume
BlocListener<DerivativeValidateOrderCubit, DerivativeValidateOrderState>(
  listenWhen: (previous, current) => previous.currentVolume != current.currentVolume,
  listener: (context, state) {
    if (state.currentVolume != null) {
      _volumeController.text = state.currentVolume!;
      _volumeController.selection = TextSelection.fromPosition(
        TextPosition(offset: _volumeController.text.length),
      );
    }
  },
),
```

### 5. Thêm Error Handling:
```dart
// Error display cho volume
BlocBuilder<DerivativeValidateOrderCubit, DerivativeValidateOrderState>(
  buildWhen: (previous, current) =>
      previous.errorVolume != current.errorVolume ||
      previous.currentVolume != current.currentVolume,
  builder: (context, state) {
    return InputFieldError(
      errorMessage: state.errorVolume.message(''),
      text: _volumeController.text,
      isShake: true,
    );
  },
),

// Error display cho order price
BlocBuilder<DerivativeValidateOrderCubit, DerivativeValidateOrderState>(
  buildWhen: (previous, current) =>
      previous.errorPrice != current.errorPrice ||
      previous.currentPrice != current.currentPrice,
  builder: (context, state) {
    return InputFieldError(
      errorMessage: state.errorPrice.message,
      text: _orderPriceController.text,
      isShake: true,
    );
  },
),
```

### 6. Cập nhật Validation Logic:
```dart
// Trước: chỉ kiểm tra activation price
final isValid = !validateState.errorActivePrice.isError &&
    _activationPriceController.text.isNotEmpty;

// Sau: kiểm tra tất cả trường
final isValid = !validateState.errorActivePrice.isError &&
    !validateState.errorPrice.isError &&
    !validateState.errorVolume.isError &&
    _activationPriceController.text.isNotEmpty &&
    _orderPriceController.text.isNotEmpty &&
    _volumeController.text.isNotEmpty;
```

### 7. Cập nhật _handleConfirm để sử dụng giá trị từ controllers:
```dart
conditionInfo: ConditionInfo(
  symbol: widget.model!.symbol ?? '',
  qty: int.tryParse(_volumeController.text) ?? widget.model!.qty ?? 0,
  side: widget.model!.side ?? '',
  type: 'STO',
  price: _orderPriceController.text.isNotEmpty
      ? _orderPriceController.text
      : widget.model!.price,
  fromDate: widget.model!.fromDate ?? '',
  toDate: widget.model!.toDate ?? '',
  activePrice: double.tryParse(_activationPriceController.text),
  activeType: validateState.activationType.toParamRequest(),
),
```

### 8. Cải thiện UI - Error display ngay dưới từng input:
```dart
// Trước: Tất cả error hiển thị ở cuối
Column(
  children: [
    _buildInputFieldsSection(),
    // All errors here...
  ],
)

// Sau: Error hiển thị ngay dưới từng input
Widget _buildVolumeInput() {
  return Column(
    children: [
      Row(/* Volume input */),
      // Error display for volume ngay dưới
      BlocBuilder<DerivativeValidateOrderCubit, DerivativeValidateOrderState>(
        builder: (context, state) {
          return InputFieldError(
            errorMessage: state.errorVolume.message(''),
            text: _volumeController.text,
            isShake: true,
          );
        },
      ),
    ],
  );
}
```

## Cách test:
1. Mở màn hình Derivatives Order Book
2. Chuyển sang tab Conditional Orders
3. Tìm một Stop Order và bấm Edit
4. **Test tất cả 3 trường:**
   - ✅ **Khối lượng đặt**: Thử bấm nút +/-
   - ✅ **Giá đặt**: Thử bấm nút +/-
   - ✅ **Giá kích hoạt**: Thử bấm nút +/-
5. **Test UI cải thiện:**
   - ✅ Nhập giá trị không hợp lệ vào từng trường
   - ✅ Kiểm tra error message hiển thị ngay dưới ô input tương ứng
   - ✅ Không còn error tập trung ở cuối form
6. Test submit với dữ liệu hợp lệ

## Kết quả mong đợi:
- ✅ Tất cả nút tăng/giảm hoạt động bình thường
- ✅ Volume tăng/giảm theo step 1
- ✅ Price tăng/giảm theo step price của derivative
- ✅ Activation price tăng/giảm theo step price
- ✅ **Error handling hiển thị ngay dưới từng input field**
- ✅ **UI trực quan hơn, dễ nhận biết lỗi**
- ✅ Validation kiểm tra tất cả trường
- ✅ Submit sử dụng giá trị từ form thay vì model cũ

## So sánh với implementation chuẩn:
Bây giờ StopOrderEditDialog đã có đầy đủ functionality như DerivativesOrderInputSection trong regular order edit, bao gồm:
- Proper cubit method calls
- State synchronization với BlocListener
- Error handling
- Complete validation
